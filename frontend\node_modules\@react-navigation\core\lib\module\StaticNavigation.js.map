{"version": 3, "names": ["React", "isValidElementType", "useRoute", "jsx", "_jsx", "MemoizedScreen", "memo", "component", "route", "children", "createElement", "displayName", "getItemsFromScreens", "Screen", "screens", "Object", "entries", "map", "name", "item", "props", "useIf", "isNavigator", "screen", "if", "_if", "rest", "createComponentForStaticNavigation", "Error", "element", "shouldRender", "tree", "Navigator", "Group", "config", "groups", "items", "key", "push", "group", "groupItems", "navigationKey", "NavigatorComponent", "createPathConfigForStaticNavigation", "options", "auto", "initialScreenConfig", "createPathConfigForTree", "t", "o", "skipInitialDetection", "createPathConfigForScreens", "initialRouteName", "fromEntries", "sort", "a", "b", "screenConfig", "linking", "path", "assign", "replace", "skipInitialDetectionInChild", "undefined", "toLowerCase", "filter", "keys", "length", "for<PERSON>ach"], "sourceRoot": "../../src", "sources": ["StaticNavigation.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,UAAU;AAa7C,SAASC,QAAQ,QAAQ,eAAY;;AAErC;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;;AAqPA;AACA;AACA;AACA;;AAOA;AACA;AACA;AAFA,SAAAC,GAAA,IAAAC,IAAA;AA0BA,MAAMC,cAAc,gBAAGL,KAAK,CAACM,IAAI,CAC/B,CAAqC;EAAEC;AAA4B,CAAC,KAAK;EACvE,MAAMC,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAMO,QAAQ,gBAAGT,KAAK,CAACU,aAAa,CAACH,SAAS,EAAE;IAAEC;EAAM,CAAC,CAAC;EAE1D,OAAOC,QAAQ;AACjB,CACF,CAAC;AAEDJ,cAAc,CAACM,WAAW,GAAG,cAAc;AAE3C,MAAMC,mBAAmB,GAAGA,CAC1BC,MAAgC,EAChCC,OAAqD,KAClD;EACH,OAAOC,MAAM,CAACC,OAAO,CAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,IAAI,CAAC,KAAK;IACnD,IAAIZ,SAA+C;IACnD,IAAIa,KAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,KAAkC;IAEtC,IAAIC,WAAW,GAAG,KAAK;IAEvB,IAAI,QAAQ,IAAIH,IAAI,EAAE;MACpB,MAAM;QAAEI,MAAM;QAAEC,EAAE,EAAEC,GAAG;QAAE,GAAGC;MAAK,CAAC,GAAGP,IAAI;MAEzCE,KAAK,GAAGI,GAAG;MACXL,KAAK,GAAGM,IAAI;MAEZ,IAAIzB,kBAAkB,CAACsB,MAAM,CAAC,EAAE;QAC9BhB,SAAS,GAAGgB,MAAM;MACpB,CAAC,MAAM,IAAI,QAAQ,IAAIA,MAAM,EAAE;QAC7BD,WAAW,GAAG,IAAI;QAClBf,SAAS,GAAGoB,kCAAkC,CAC5CJ,MAAM,EACN,GAAGL,IAAI,WACT,CAAC;MACH;IACF,CAAC,MAAM,IAAIjB,kBAAkB,CAACkB,IAAI,CAAC,EAAE;MACnCZ,SAAS,GAAGY,IAAI;IAClB,CAAC,MAAM,IAAI,QAAQ,IAAIA,IAAI,EAAE;MAC3BG,WAAW,GAAG,IAAI;MAClBf,SAAS,GAAGoB,kCAAkC,CAACR,IAAI,EAAE,GAAGD,IAAI,WAAW,CAAC;IAC1E;IAEA,IAAIX,SAAS,IAAI,IAAI,EAAE;MACrB,MAAM,IAAIqB,KAAK,CACb,qDAAqDV,IAAI,qLAC3D,CAAC;IACH;IAEA,MAAMW,OAAO,GAAGP,WAAW,iBACzBtB,KAAK,CAACU,aAAa,CAACH,SAAS,EAAE,CAAC,CAAC,CAAC,iBAElCH,IAAA,CAACC,cAAc;MAACE,SAAS,EAAEA;IAAU,CAAE,CACxC;IAED,OAAO,MAAM;MACX,MAAMuB,YAAY,GAAGT,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC;MAE7C,IAAI,CAACS,YAAY,EAAE;QACjB,OAAO,IAAI;MACb;MAEA,oBACE1B,IAAA,CAACS,MAAM;QAAYK,IAAI,EAAEA,IAAK;QAAA,GAAKE,KAAK;QAAAX,QAAA,EACrCA,CAAA,KAAMoB;MAAO,GADHX,IAEL,CAAC;IAEb,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,kCAAkCA,CAChDI,IAAqC,EACrCpB,WAAmB,EACM;EACzB,MAAM;IAAEqB,SAAS;IAAEC,KAAK;IAAEpB,MAAM;IAAEqB;EAAO,CAAC,GAAGH,IAAI;EACjD,MAAM;IAAEjB,OAAO;IAAEqB,MAAM;IAAE,GAAGT;EAAK,CAAC,GAAGQ,MAAM;EAE3C,IAAIpB,OAAO,IAAI,IAAI,IAAIqB,MAAM,IAAI,IAAI,EAAE;IACrC,MAAM,IAAIP,KAAK,CACb,mIACF,CAAC;EACH;EAEA,MAAMQ,KAAyC,GAAG,EAAE;;EAEpD;EACA;EACA,KAAK,MAAMC,GAAG,IAAIH,MAAM,EAAE;IACxB,IAAIG,GAAG,KAAK,SAAS,IAAIvB,OAAO,EAAE;MAChCsB,KAAK,CAACE,IAAI,CAAC,GAAG1B,mBAAmB,CAACC,MAAM,EAAEC,OAAO,CAAC,CAAC;IACrD;IAEA,IAAIuB,GAAG,KAAK,QAAQ,IAAIF,MAAM,EAAE;MAC9BC,KAAK,CAACE,IAAI,CACR,GAAGvB,MAAM,CAACC,OAAO,CAACmB,MAAM,CAAC,CAAClB,GAAG,CAAC,CAAC,CAACoB,GAAG,EAAE;QAAEb,EAAE,EAAEH,KAAK;QAAE,GAAGkB;MAAM,CAAC,CAAC,KAAK;QAChE,MAAMC,UAAU,GAAG5B,mBAAmB,CAACC,MAAM,EAAE0B,KAAK,CAACzB,OAAO,CAAC;QAE7D,OAAO,MAAM;UACX;UACA,MAAML,QAAQ,GAAG+B,UAAU,CAACvB,GAAG,CAAEE,IAAI,IAAKA,IAAI,CAAC,CAAC,CAAC;UAEjD,MAAMW,YAAY,GAAGT,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC,CAAC;UAE7C,IAAI,CAACS,YAAY,EAAE;YACjB,OAAO,IAAI;UACb;UAEA,oBACE1B,IAAA,CAAC6B,KAAK;YAAWQ,aAAa,EAAEJ,GAAI;YAAA,GAAKE,KAAK;YAAA9B,QAAA,EAC3CA;UAAQ,GADC4B,GAEL,CAAC;QAEZ,CAAC;MACH,CAAC,CACH,CAAC;IACH;EACF;EAEA,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMjC,QAAQ,GAAG2B,KAAK,CAACnB,GAAG,CAAEE,IAAI,IAAKA,IAAI,CAAC,CAAC,CAAC;IAE5C,oBAAOf,IAAA,CAAC4B,SAAS;MAAA,GAAKN,IAAI;MAAAjB,QAAA,EAAGA;IAAQ,CAAY,CAAC;EACpD,CAAC;EAEDiC,kBAAkB,CAAC/B,WAAW,GAAGA,WAAW;EAE5C,OAAO+B,kBAAkB;AAC3B;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mCAAmCA,CACjDZ,IAAuB,EACvBa,OAEC,EACDC,IAAc,EACd;EACA,IAAIC,mBAA0D;EAE9D,MAAMC,uBAAuB,GAAGA,CAC9BC,CAAoB,EACpBC,CAA4C,EAG5CC,oBAA6B,KAC1B;IACH,MAAMC,0BAA0B,GAAGA,CACjCrC,OAMC,EACDsC,gBAAoC,KACjC;MACH,OAAOrC,MAAM,CAACsC,WAAW,CACvBtC,MAAM,CAACC,OAAO,CAACF,OAAO;MACpB;MACA;MAAA,CACCwC,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAK;QAClB,IAAID,CAAC,KAAKH,gBAAgB,EAAE;UAC1B,OAAO,CAAC,CAAC;QACX;QAEA,IAAII,CAAC,KAAKJ,gBAAgB,EAAE;UAC1B,OAAO,CAAC;QACV;QAEA,OAAO,CAAC;MACV,CAAC,CAAC,CACDnC,GAAG,CAAC,CAAC,CAACoB,GAAG,EAAElB,IAAI,CAAC,KAAK;QACpB,MAAMsC,YAAuC,GAAG,CAAC,CAAC;QAElD,IAAI,SAAS,IAAItC,IAAI,EAAE;UACrB,IAAI,OAAOA,IAAI,CAACuC,OAAO,KAAK,QAAQ,EAAE;YACpCD,YAAY,CAACE,IAAI,GAAGxC,IAAI,CAACuC,OAAO;UAClC,CAAC,MAAM;YACL3C,MAAM,CAAC6C,MAAM,CAACH,YAAY,EAAEtC,IAAI,CAACuC,OAAO,CAAC;UAC3C;UAEA,IAAI,OAAOD,YAAY,CAACE,IAAI,KAAK,QAAQ,EAAE;YACzCF,YAAY,CAACE,IAAI,GAAGF,YAAY,CAACE,IAAI,CAClCE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAAA,CACnBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;UACzB;QACF;QAEA,IAAI/C,OAAO;QAEX,MAAMgD,2BAA2B,GAC/BZ,oBAAoB,IACnBO,YAAY,CAACE,IAAI,IAAI,IAAI,IAAIF,YAAY,CAACE,IAAI,KAAK,EAAG;QAEzD,IAAI,QAAQ,IAAIxC,IAAI,EAAE;UACpBL,OAAO,GAAGiC,uBAAuB,CAC/B5B,IAAI,EACJ4C,SAAS,EACTD,2BACF,CAAC;QACH,CAAC,MAAM,IACL,QAAQ,IAAI3C,IAAI,IAChB,QAAQ,IAAIA,IAAI,CAACI,MAAM,KACtBJ,IAAI,CAACI,MAAM,CAACW,MAAM,CAACpB,OAAO,IAAIK,IAAI,CAACI,MAAM,CAACW,MAAM,CAACC,MAAM,CAAC,EACzD;UACArB,OAAO,GAAGiC,uBAAuB,CAC/B5B,IAAI,CAACI,MAAM,EACXwC,SAAS,EACTD,2BACF,CAAC;QACH;QAEA,IAAIhD,OAAO,EAAE;UACX2C,YAAY,CAAC3C,OAAO,GAAGA,OAAO;QAChC;QAEA,IACE+B,IAAI,IACJ,CAACY,YAAY,CAAC3C,OAAO;QACrB;QACA,EAAE,SAAS,IAAIK,IAAI,IAAIA,IAAI,CAACuC,OAAO,IAAI,IAAI,CAAC,EAC5C;UACA,IAAID,YAAY,CAACE,IAAI,IAAI,IAAI,EAAE;YAC7B,IAAI,CAACT,oBAAoB,IAAIO,YAAY,CAACE,IAAI,KAAK,EAAE,EAAE;cACrD;cACA;cACAb,mBAAmB,GAAGiB,SAAS;YACjC;UACF,CAAC,MAAM;YACL,IAAI,CAACb,oBAAoB,IAAIJ,mBAAmB,IAAI,IAAI,EAAE;cACxDA,mBAAmB,GAAGW,YAAY;YACpC;YAEAA,YAAY,CAACE,IAAI,GAAGtB,GAAG,CACpBwB,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAC3BA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CACjBG,WAAW,CAAC,CAAC;UAClB;QACF;QAEA,OAAO,CAAC3B,GAAG,EAAEoB,YAAY,CAAC;MAC5B,CAAC,CAAC,CACDQ,MAAM,CAAC,CAAC,GAAG1C,MAAM,CAAC,KAAKR,MAAM,CAACmD,IAAI,CAAC3C,MAAM,CAAC,CAAC4C,MAAM,GAAG,CAAC,CAC1D,CAAC;IACH,CAAC;IAED,MAAMrD,OAAO,GAAG,CAAC,CAAC;;IAElB;IACA;IACA,KAAK,MAAMuB,GAAG,IAAIW,CAAC,CAACd,MAAM,EAAE;MAC1B,IAAIG,GAAG,KAAK,SAAS,IAAIW,CAAC,CAACd,MAAM,CAACpB,OAAO,EAAE;QACzCC,MAAM,CAAC6C,MAAM,CACX9C,OAAO,EACPqC,0BAA0B,CACxBH,CAAC,CAACd,MAAM,CAACpB,OAAO,EAChBmC,CAAC,EAAEG,gBAAgB,IAAIJ,CAAC,CAACd,MAAM,CAACkB,gBAClC,CACF,CAAC;MACH;MAEA,IAAIf,GAAG,KAAK,QAAQ,IAAIW,CAAC,CAACd,MAAM,CAACC,MAAM,EAAE;QACvCpB,MAAM,CAACC,OAAO,CAACgC,CAAC,CAACd,MAAM,CAACC,MAAM,CAAC,CAACiC,OAAO,CAAC,CAAC,GAAG7B,KAAK,CAAC,KAAK;UACrDxB,MAAM,CAAC6C,MAAM,CACX9C,OAAO,EACPqC,0BAA0B,CACxBZ,KAAK,CAACzB,OAAO,EACbmC,CAAC,EAAEG,gBAAgB,IAAIJ,CAAC,CAACd,MAAM,CAACkB,gBAClC,CACF,CAAC;QACH,CAAC,CAAC;MACJ;IACF;IAEA,IAAIrC,MAAM,CAACmD,IAAI,CAACpD,OAAO,CAAC,CAACqD,MAAM,KAAK,CAAC,EAAE;MACrC,OAAOJ,SAAS;IAClB;IAEA,OAAOjD,OAAO;EAChB,CAAC;EAED,MAAMA,OAAO,GAAGiC,uBAAuB,CAAChB,IAAI,EAAEa,OAAO,EAAE,KAAK,CAAC;EAE7D,IAAIC,IAAI,IAAIC,mBAAmB,EAAE;IAC/BA,mBAAmB,CAACa,IAAI,GAAG,EAAE;EAC/B;EAEA,OAAO7C,OAAO;AAChB", "ignoreList": []}