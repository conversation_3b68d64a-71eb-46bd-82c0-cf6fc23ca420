{"version": 3, "names": ["isPlainObject", "value", "Object", "getPrototypeOf", "prototype", "deepFreeze", "object", "process", "env", "NODE_ENV", "isFrozen", "Array", "isArray", "key", "getOwnPropertyDescriptor", "configurable", "freeze"], "sourceRoot": "../../src", "sources": ["deepFreeze.tsx"], "mappings": ";;AAAA,OAAO,MAAMA,aAAa,GAAIC,KAAc,IAAsB;EAChE,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAC/C,OAAOC,MAAM,CAACC,cAAc,CAACF,KAAK,CAAC,KAAKC,MAAM,CAACE,SAAS;EAC1D;EAEA,OAAO,KAAK;AACd,CAAC;AAED,OAAO,MAAMC,UAAU,GAAQC,MAAS,IAAkB;EACxD;EACA;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOH,MAAM;EACf;EAEA,IAAIJ,MAAM,CAACQ,QAAQ,CAACJ,MAAM,CAAC,EAAE;IAC3B,OAAOA,MAAM;EACf;EAEA,IAAI,CAACN,aAAa,CAACM,MAAM,CAAC,IAAI,CAACK,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;IACpD,OAAOA,MAAM;EACf;;EAEA;EACA,KAAK,MAAMO,GAAG,IAAIP,MAAM,EAAE;IACxB;IACA,IAAIO,GAAG,KAAK,QAAQ,EAAE;MACpB,IAAIX,MAAM,CAACY,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,EAAEE,YAAY,EAAE;QAC9D,MAAMd,KAAK,GAAGK,MAAM,CAACO,GAAG,CAAC;QAEzBR,UAAU,CAACJ,KAAK,CAAC;MACnB;IACF;EACF;EAEA,OAAOC,MAAM,CAACc,MAAM,CAACV,MAAM,CAAC;AAC9B,CAAC", "ignoreList": []}