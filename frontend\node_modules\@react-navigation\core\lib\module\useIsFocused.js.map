{"version": 3, "names": ["React", "useNavigation", "useIsFocused", "navigation", "subscribe", "useCallback", "callback", "unsubscribeFocus", "addListener", "unsubscribeBlur", "value", "useSyncExternalStore", "isFocused"], "sourceRoot": "../../src", "sources": ["useIsFocused.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAa,QAAQ,oBAAiB;;AAE/C;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAA,EAAY;EACtC,MAAMC,UAAU,GAAGF,aAAa,CAAC,CAAC;EAElC,MAAMG,SAAS,GAAGJ,KAAK,CAACK,WAAW,CAChCC,QAAoB,IAAK;IACxB,MAAMC,gBAAgB,GAAGJ,UAAU,CAACK,WAAW,CAAC,OAAO,EAAEF,QAAQ,CAAC;IAClE,MAAMG,eAAe,GAAGN,UAAU,CAACK,WAAW,CAAC,MAAM,EAAEF,QAAQ,CAAC;IAEhE,OAAO,MAAM;MACXC,gBAAgB,CAAC,CAAC;MAClBE,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EACD,CAACN,UAAU,CACb,CAAC;EAED,MAAMO,KAAK,GAAGV,KAAK,CAACW,oBAAoB,CACtCP,SAAS,EACTD,UAAU,CAACS,SAAS,EACpBT,UAAU,CAACS,SACb,CAAC;EAED,OAAOF,KAAK;AACd", "ignoreList": []}