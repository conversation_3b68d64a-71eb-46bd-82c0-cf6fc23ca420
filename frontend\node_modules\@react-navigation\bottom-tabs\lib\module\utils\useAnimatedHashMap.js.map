{"version": 3, "names": ["React", "Animated", "useAnimatedHashMap", "routes", "index", "refs", "useRef", "previous", "current", "routeKeys", "Object", "keys", "length", "every", "route", "includes", "key", "for<PERSON>ach", "i", "Value"], "sourceRoot": "../../../src", "sources": ["utils/useAnimatedHashMap.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,cAAc;AAEvC,OAAO,SAASC,kBAAkBA,CAAC;EAAEC,MAAM;EAAEC;AAAuB,CAAC,EAAE;EACrE,MAAMC,IAAI,GAAGL,KAAK,CAACM,MAAM,CAAiC,CAAC,CAAC,CAAC;EAC7D,MAAMC,QAAQ,GAAGF,IAAI,CAACG,OAAO;EAC7B,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC;EAEvC,IACEJ,MAAM,CAACS,MAAM,KAAKH,SAAS,CAACG,MAAM,IAClCT,MAAM,CAACU,KAAK,CAAEC,KAAK,IAAKL,SAAS,CAACM,QAAQ,CAACD,KAAK,CAACE,GAAG,CAAC,CAAC,EACtD;IACA,OAAOT,QAAQ;EACjB;EACAF,IAAI,CAACG,OAAO,GAAG,CAAC,CAAC;EAEjBL,MAAM,CAACc,OAAO,CAAC,CAAC;IAAED;EAAI,CAAC,EAAEE,CAAC,KAAK;IAC7Bb,IAAI,CAACG,OAAO,CAACQ,GAAG,CAAC,GACfT,QAAQ,CAACS,GAAG,CAAC,IACb,IAAIf,QAAQ,CAACkB,KAAK,CAACD,CAAC,KAAKd,KAAK,GAAG,CAAC,GAAGc,CAAC,IAAId,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC;EAEF,OAAOC,IAAI,CAACG,OAAO;AACrB", "ignoreList": []}