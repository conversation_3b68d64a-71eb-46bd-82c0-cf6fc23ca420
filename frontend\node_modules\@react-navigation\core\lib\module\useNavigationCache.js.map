{"version": 3, "names": ["CommonActions", "React", "NavigationBuilderContext", "useNavigationCache", "state", "getState", "navigation", "setOptions", "router", "emitter", "stackRef", "useContext", "base", "useMemo", "emit", "rest", "actions", "actionCreators", "dispatch", "Error", "helpers", "Object", "keys", "reduce", "acc", "name", "addListener", "removeListener", "getParent", "id", "undefined", "getId", "isFocused", "cache", "current", "routes", "route", "previous", "key", "thunk", "action", "source", "withStack", "callback", "isStackSet", "process", "env", "NODE_ENV", "stack", "args", "create", "options", "o", "index", "navigations"], "sourceRoot": "../../src", "sources": ["useNavigationCache.tsx"], "mappings": ";;AAAA,SACEA,aAAa,QAKR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwB,QAAQ,+BAA4B;AAyCrE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAKhC;EACAC,KAAK;EACLC,QAAQ;EACRC,UAAU;EACVC,UAAU;EACVC,MAAM;EACNC;AACuC,CAAC,EAAE;EAC1C,MAAM;IAAEC;EAAS,CAAC,GAAGT,KAAK,CAACU,UAAU,CAACT,wBAAwB,CAAC;EAE/D,MAAMU,IAAI,GAAGX,KAAK,CAACY,OAAO,CAAC,MAKR;IACjB;IACA,MAAM;MAAEC,IAAI;MAAE,GAAGC;IAAK,CAAC,GAAGT,UAAU;IAEpC,MAAMU,OAAO,GAAG;MACd,GAAGR,MAAM,CAACS,cAAc;MACxB,GAAGjB;IACL,CAAC;IAED,MAAMkB,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAM,IAAIC,KAAK,CACb,yDACF,CAAC;IACH,CAAC;IAED,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACO,MAAM,CACzC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACbD,GAAG,CAACC,IAAI,CAAC,GAAGP,QAAQ;MAEpB,OAAOM,GAAG;IACZ,CAAC,EACD,CAAC,CACH,CAAkB;IAElB,OAAO;MACL,GAAGT,IAAI;MACP,GAAGK,OAAO;MACVM,WAAW,EAAEA,CAAA,KAAM;QACjB;;QAEA,OAAO,MAAM;UACX;QAAA,CACD;MACH,CAAC;MACDC,cAAc,EAAEA,CAAA,KAAM;QACpB;MAAA,CACD;MACDT,QAAQ;MACRU,SAAS,EAAGC,EAAW,IAAK;QAC1B,IAAIA,EAAE,KAAKC,SAAS,IAAID,EAAE,KAAKd,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAE;UAC3C,OAAOnB,IAAI;QACb;QAEA,OAAOG,IAAI,CAACa,SAAS,CAACC,EAAE,CAAC;MAC3B,CAAC;MACDtB,UAAU,EAAEA,CAAA,KAAM;QAChB,MAAM,IAAIY,KAAK,CAAC,kDAAkD,CAAC;MACrE,CAAC;MACDa,SAAS,EAAEA,CAAA,KAAM;IACnB,CAAC;EACH,CAAC,EAAE,CAAC1B,UAAU,EAAEE,MAAM,CAACS,cAAc,CAAC,CAAC;;EAEvC;EACA;EACA;EACA,MAAMgB,KAAK,GAAGhC,KAAK,CAACY,OAAO,CACzB,OAAO;IAAEqB,OAAO,EAAE,CAAC;EAAqD,CAAC,CAAC;EAC1E;EACA,CAACtB,IAAI,EAAEP,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEE,OAAO,CAClD,CAAC;EAEDwB,KAAK,CAACC,OAAO,GAAG9B,KAAK,CAAC+B,MAAM,CAACZ,MAAM,CAEjC,CAACC,GAAG,EAAEY,KAAK,KAAK;IAChB,MAAMC,QAAQ,GAAGJ,KAAK,CAACC,OAAO,CAACE,KAAK,CAACE,GAAG,CAAC;IAMzC,IAAID,QAAQ,EAAE;MACZ;MACAb,GAAG,CAACY,KAAK,CAACE,GAAG,CAAC,GAAGD,QAAQ;IAC3B,CAAC,MAAM;MACL,MAAMnB,QAAQ,GAAIqB,KAAY,IAAK;QACjC,MAAMC,MAAM,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAClC,QAAQ,CAAC,CAAC,CAAC,GAAGkC,KAAK;QAEtE,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClBlC,UAAU,CAACY,QAAQ,CAAC;YAAEuB,MAAM,EAAEL,KAAK,CAACE,GAAG;YAAE,GAAGE;UAAO,CAAC,CAAC;QACvD;MACF,CAAC;MAED,MAAME,SAAS,GAAIC,QAAoB,IAAK;QAC1C,IAAIC,UAAU,GAAG,KAAK;QAEtB,IAAI;UACF,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCrC,QAAQ,IACR,CAACA,QAAQ,CAACwB,OAAO,EACjB;YACA;YACAxB,QAAQ,CAACwB,OAAO,GAAG,IAAIf,KAAK,CAAC,CAAC,CAAC6B,KAAK;YACpCJ,UAAU,GAAG,IAAI;UACnB;UAEAD,QAAQ,CAAC,CAAC;QACZ,CAAC,SAAS;UACR,IAAIC,UAAU,IAAIlC,QAAQ,EAAE;YAC1BA,QAAQ,CAACwB,OAAO,GAAGJ,SAAS;UAC9B;QACF;MACF,CAAC;MAED,MAAMd,OAAO,GAAG;QACd,GAAGR,MAAM,CAACS,cAAc;QACxB,GAAGjB;MACL,CAAC;MAED,MAAMoB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACO,MAAM,CACzC,CAACC,GAAG,EAAEC,IAAI,KAAK;QACbD,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGwB,IAAS,KACvBP,SAAS,CAAC;QACR;QACAxB,QAAQ,CAACF,OAAO,CAACS,IAAI,CAAC,CAAC,GAAGwB,IAAI,CAAC,CACjC,CAAC;QAEH,OAAOzB,GAAG;MACZ,CAAC,EACD,CAAC,CACH,CAAC;MAEDA,GAAG,CAACY,KAAK,CAACE,GAAG,CAAC,GAAG;QACf,GAAG1B,IAAI;QACP,GAAGQ,OAAO;QACV;QACA,GAAIX,OAAO,CAACyC,MAAM,CAACd,KAAK,CAACE,GAAG,CAAS;QACrCpB,QAAQ,EAAGqB,KAAY,IAAKG,SAAS,CAAC,MAAMxB,QAAQ,CAACqB,KAAK,CAAC,CAAC;QAC5DX,SAAS,EAAGC,EAAW,IAAK;UAC1B,IAAIA,EAAE,KAAKC,SAAS,IAAID,EAAE,KAAKjB,IAAI,CAACmB,KAAK,CAAC,CAAC,EAAE;YAC3C;YACA;YACA,OAAOP,GAAG,CAACY,KAAK,CAACE,GAAG,CAAC;UACvB;UAEA,OAAO1B,IAAI,CAACgB,SAAS,CAACC,EAAE,CAAC;QAC3B,CAAC;QACDtB,UAAU,EAAG4C,OAAe,IAAK;UAC/B5C,UAAU,CAAE6C,CAAC,KAAM;YACjB,GAAGA,CAAC;YACJ,CAAChB,KAAK,CAACE,GAAG,GAAG;cAAE,GAAGc,CAAC,CAAChB,KAAK,CAACE,GAAG,CAAC;cAAE,GAAGa;YAAQ;UAC7C,CAAC,CAAC,CAAC;QACL,CAAC;QACDnB,SAAS,EAAEA,CAAA,KAAM;UACf,MAAM5B,KAAK,GAAGQ,IAAI,CAACP,QAAQ,CAAC,CAAC;UAE7B,IAAID,KAAK,CAAC+B,MAAM,CAAC/B,KAAK,CAACiD,KAAK,CAAC,CAACf,GAAG,KAAKF,KAAK,CAACE,GAAG,EAAE;YAC/C,OAAO,KAAK;UACd;;UAEA;UACA;UACA,OAAOhC,UAAU,GAAGA,UAAU,CAAC0B,SAAS,CAAC,CAAC,GAAG,IAAI;QACnD;MACF,CAAC;IACH;IAEA,OAAOR,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAO;IACLZ,IAAI;IACJ0C,WAAW,EAAErB,KAAK,CAACC;EACrB,CAAC;AACH", "ignoreList": []}