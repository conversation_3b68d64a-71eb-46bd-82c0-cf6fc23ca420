{"version": 3, "names": ["React", "PreventRemoveContext", "usePreventRemoveContext", "value", "useContext", "Error"], "sourceRoot": "../../src", "sources": ["usePreventRemoveContext.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,oBAAoB,QAAQ,2BAAwB;AAE7D,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,oBAAoB,CAAC;EAEpD,IAAIE,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIE,KAAK,CACb,uFACF,CAAC;EACH;EAEA,OAAOF,KAAK;AACd", "ignoreList": []}