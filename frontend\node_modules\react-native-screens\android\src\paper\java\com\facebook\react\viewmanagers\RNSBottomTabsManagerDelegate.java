/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNSBottomTabsManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNSBottomTabsManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNSBottomTabsManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "tabBarBackgroundColor":
        mViewManager.setTabBarBackgroundColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarBlurEffect":
        mViewManager.setTabBarBlurEffect(view, (String) value);
        break;
      case "tabBarTintColor":
        mViewManager.setTabBarTintColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemTitleFontFamily":
        mViewManager.setTabBarItemTitleFontFamily(view, value == null ? null : (String) value);
        break;
      case "tabBarItemTitleFontSize":
        mViewManager.setTabBarItemTitleFontSize(view, value == null ? 0f : ((Double) value).floatValue());
        break;
      case "tabBarItemTitleFontWeight":
        mViewManager.setTabBarItemTitleFontWeight(view, value == null ? null : (String) value);
        break;
      case "tabBarItemTitleFontStyle":
        mViewManager.setTabBarItemTitleFontStyle(view, value == null ? null : (String) value);
        break;
      case "tabBarItemTitleFontColor":
        mViewManager.setTabBarItemTitleFontColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemTitlePositionAdjustment":
        mViewManager.setTabBarItemTitlePositionAdjustment(view, (ReadableMap) value);
        break;
      case "tabBarItemIconColor":
        mViewManager.setTabBarItemIconColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemBadgeBackgroundColor":
        mViewManager.setTabBarItemBadgeBackgroundColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemTitleFontColorActive":
        mViewManager.setTabBarItemTitleFontColorActive(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemIconColorActive":
        mViewManager.setTabBarItemIconColorActive(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemTitleFontSizeActive":
        mViewManager.setTabBarItemTitleFontSizeActive(view, value == null ? 0f : ((Double) value).floatValue());
        break;
      case "controlNavigationStateInJS":
        mViewManager.setControlNavigationStateInJS(view, value == null ? false : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
