import "./global.css";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import DonorListScreen from "./components/DonorListScreen";
import DonorRegistrationScreen from "./components/DonorRegistrationScreen";
import HealthCheckScreen from "./components/HealthCheckScreen";
import DonorDetailScreen from "./components/";

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="DonorList">
        <Stack.Screen name="DonorList" component={DonorListScreen} options={{ title: "Donor List" }} />
        <Stack.Screen name="DonorRegistration" component={DonorRegistrationScreen} options={{ title: "Register Donor" }} />
        <Stack.Screen name="DonorDetail" component={DonorDetailScreen} options={{ title: "Donor Details" }} />
        <Stack.Screen name="HealthCheck" component={HealthCheckScreen} options={{ title: "Health Check" }} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}


