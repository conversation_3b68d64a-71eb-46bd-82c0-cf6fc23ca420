{"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "jsx", "_jsx", "FAR_FAR_AWAY", "ResourceSavingView", "visible", "children", "style", "rest", "OS", "hidden", "display", "styles", "container", "pointerEvents", "collapsable", "removeClippedSubviews", "attached", "detached", "create", "flex", "overflow", "top"], "sourceRoot": "../../src", "sources": ["ResourceSavingView.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAQtB,MAAMC,YAAY,GAAG,KAAK,CAAC,CAAC;;AAE5B,OAAO,SAASC,kBAAkBA,CAAC;EACjCC,OAAO;EACPC,QAAQ;EACRC,KAAK;EACL,GAAGC;AACE,CAAC,EAAE;EACR,IAAIV,QAAQ,CAACW,EAAE,KAAK,KAAK,EAAE;IACzB,oBACEP,IAAA,CAACF;IACC;IAAA;MACAU,MAAM,EAAE,CAACL,OAAQ;MACjBE,KAAK,EAAE,CACL;QAAEI,OAAO,EAAEN,OAAO,GAAG,MAAM,GAAG;MAAO,CAAC,EACtCO,MAAM,CAACC,SAAS,EAChBN,KAAK,CACL;MACFO,aAAa,EAAET,OAAO,GAAG,MAAM,GAAG,MAAO;MAAA,GACrCG,IAAI;MAAAF,QAAA,EAEPA;IAAQ,CACL,CAAC;EAEX;EAEA,oBACEJ,IAAA,CAACF,IAAI;IACHO,KAAK,EAAE,CAACK,MAAM,CAACC,SAAS,EAAEN,KAAK;IAC/B;IAAA;IACAO,aAAa,EAAET,OAAO,GAAG,MAAM,GAAG,MAAO;IAAAC,QAAA,eAEzCJ,IAAA,CAACF,IAAI;MACHe,WAAW,EAAE,KAAM;MACnBC,qBAAqB;MACnB;MACA;MACAlB,QAAQ,CAACW,EAAE,KAAK,KAAK,IAAIX,QAAQ,CAACW,EAAE,KAAK,OAAO,GAAG,CAACJ,OAAO,GAAG,IAC/D;MACDS,aAAa,EAAET,OAAO,GAAG,MAAM,GAAG,MAAO;MACzCE,KAAK,EAAEF,OAAO,GAAGO,MAAM,CAACK,QAAQ,GAAGL,MAAM,CAACM,QAAS;MAAAZ,QAAA,EAElDA;IAAQ,CACL;EAAC,CACH,CAAC;AAEX;AAEA,MAAMM,MAAM,GAAGb,UAAU,CAACoB,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC;EACDJ,QAAQ,EAAE;IACRG,IAAI,EAAE;EACR,CAAC;EACDF,QAAQ,EAAE;IACRE,IAAI,EAAE,CAAC;IACPE,GAAG,EAAEnB;EACP;AACF,CAAC,CAAC", "ignoreList": []}