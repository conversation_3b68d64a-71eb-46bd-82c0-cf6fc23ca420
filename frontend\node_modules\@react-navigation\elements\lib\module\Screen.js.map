{"version": 3, "names": ["NavigationContext", "NavigationRouteContext", "React", "StyleSheet", "View", "useSafeAreaInsets", "Background", "getDefaultHeaderHeight", "HeaderHeightContext", "HeaderShownContext", "useFrameSize", "jsx", "_jsx", "jsxs", "_jsxs", "Screen", "props", "insets", "isParentHeaderShown", "useContext", "parentHeaderHeight", "focused", "modal", "header", "headerShown", "headerTransparent", "headerStatusBarHeight", "top", "navigation", "route", "children", "style", "defaultHeaderHeight", "size", "headerHeight", "setHeaderHeight", "useState", "styles", "container", "collapsable", "Provider", "value", "pointerEvents", "onLayout", "e", "height", "nativeEvent", "layout", "absolute", "content", "create", "flex", "zIndex", "position", "start", "end"], "sourceRoot": "../../src", "sources": ["Screen.tsx"], "mappings": ";;AAAA,SACEA,iBAAiB,EAEjBC,sBAAsB,QAGjB,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAGEC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,SAASC,UAAU,QAAQ,iBAAc;AACzC,SAASC,sBAAsB,QAAQ,oCAAiC;AACxE,SAASC,mBAAmB,QAAQ,iCAA8B;AAClE,SAASC,kBAAkB,QAAQ,gCAA6B;AAChE,SAASC,YAAY,QAAQ,mBAAgB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAe9C,OAAO,SAASC,MAAMA,CAACC,KAAY,EAAE;EACnC,MAAMC,MAAM,GAAGZ,iBAAiB,CAAC,CAAC;EAElC,MAAMa,mBAAmB,GAAGhB,KAAK,CAACiB,UAAU,CAACV,kBAAkB,CAAC;EAChE,MAAMW,kBAAkB,GAAGlB,KAAK,CAACiB,UAAU,CAACX,mBAAmB,CAAC;EAEhE,MAAM;IACJa,OAAO;IACPC,KAAK,GAAG,KAAK;IACbC,MAAM;IACNC,WAAW,GAAG,IAAI;IAClBC,iBAAiB;IACjBC,qBAAqB,GAAGR,mBAAmB,GAAG,CAAC,GAAGD,MAAM,CAACU,GAAG;IAC5DC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGf,KAAK;EAET,MAAMgB,mBAAmB,GAAGtB,YAAY,CAAEuB,IAAI,IAC5C1B,sBAAsB,CAAC0B,IAAI,EAAEX,KAAK,EAAEI,qBAAqB,CAC3D,CAAC;EAED,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGjC,KAAK,CAACkC,QAAQ,CAACJ,mBAAmB,CAAC;EAE3E,oBACElB,KAAA,CAACR,UAAU;IACT,eAAa,CAACe,OAAQ;IACtBU,KAAK,EAAE,CAACM,MAAM,CAACC,SAAS,EAAEP,KAAK;IAC/B;IACA;IAAA;IACAQ,WAAW,EAAE,KAAM;IAAAT,QAAA,GAElBN,WAAW,gBACVZ,IAAA,CAACZ,iBAAiB,CAACwC,QAAQ;MAACC,KAAK,EAAEb,UAAW;MAAAE,QAAA,eAC5ClB,IAAA,CAACX,sBAAsB,CAACuC,QAAQ;QAACC,KAAK,EAAEZ,KAAM;QAAAC,QAAA,eAC5ClB,IAAA,CAACR,IAAI;UACHsC,aAAa,EAAC,UAAU;UACxBC,QAAQ,EAAGC,CAAC,IAAK;YACf,MAAM;cAAEC;YAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAACC,MAAM;YAEvCZ,eAAe,CAACU,MAAM,CAAC;UACzB,CAAE;UACFd,KAAK,EAAE,CACLM,MAAM,CAACd,MAAM,EACbE,iBAAiB,GAAGY,MAAM,CAACW,QAAQ,GAAG,IAAI,CAC1C;UAAAlB,QAAA,EAEDP;QAAM,CACH;MAAC,CACwB;IAAC,CACR,CAAC,GAC3B,IAAI,eACRX,IAAA,CAACR,IAAI;MAAC2B,KAAK,EAAEM,MAAM,CAACY,OAAQ;MAAAnB,QAAA,eAC1BlB,IAAA,CAACH,kBAAkB,CAAC+B,QAAQ;QAC1BC,KAAK,EAAEvB,mBAAmB,IAAIM,WAAW,KAAK,KAAM;QAAAM,QAAA,eAEpDlB,IAAA,CAACJ,mBAAmB,CAACgC,QAAQ;UAC3BC,KAAK,EAAEjB,WAAW,GAAGU,YAAY,GAAId,kBAAkB,IAAI,CAAG;UAAAU,QAAA,EAE7DA;QAAQ,CACmB;MAAC,CACJ;IAAC,CAC1B,CAAC;EAAA,CACG,CAAC;AAEjB;AAEA,MAAMO,MAAM,GAAGlC,UAAU,CAAC+C,MAAM,CAAC;EAC/BZ,SAAS,EAAE;IACTa,IAAI,EAAE;EACR,CAAC;EACDF,OAAO,EAAE;IACPE,IAAI,EAAE;EACR,CAAC;EACD5B,MAAM,EAAE;IACN6B,MAAM,EAAE;EACV,CAAC;EACDJ,QAAQ,EAAE;IACRK,QAAQ,EAAE,UAAU;IACpB1B,GAAG,EAAE,CAAC;IACN2B,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}