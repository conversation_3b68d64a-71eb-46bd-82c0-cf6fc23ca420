import React, { useState, useEffect } from "react";
import { View, Text, Alert, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from "react-native";
import { useNavigation } from "@react-navigation/native";
import {
  Button,
  Input,
  BloodGroupSelector,
  DatePicker,
  ProgressIndicator,
  LoadingSpinner,
  Card
} from "./ui";

interface LocationData {
  id: number;
  name: string;
}

export default function DonorRegistrationScreen() {
  const [currentStep, setCurrentStep] = useState(1);
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    bloodGroup: "",
    divisionId: "",
    zilaId: "",
    upazilaId: "",
    village: "",
    currentLocation: "",
    lastDonationDate: "",
    phoneNumber: "",
    isAvailable: true,
    notes: "",
    consent: false,
  });
  const [divisions, setDivisions] = useState<LocationData[]>([]);
  const [zilas, setZilas] = useState<LocationData[]>([]);
  const [upazilas, setUpazilas] = useState<LocationData[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const navigation = useNavigation();

  const stepTitles = ["Personal Info", "Blood & Health", "Location", "Review & Submit"];
  const totalSteps = 4;

  useEffect(() => {
    fetch("http://localhost:5000/api/divisions").then(res => res.json()).then(setDivisions);
  }, []);
  useEffect(() => {
    if (form.divisionId) {
      fetch(`http://localhost:5000/api/zilas/${form.divisionId}`).then(res => res.json()).then(setZilas);
    } else {
      setZilas([]);
    }
    setForm(f => ({ ...f, zilaId: "", upazilaId: "" }));
  }, [form.divisionId]);
  useEffect(() => {
    if (form.zilaId) {
      fetch(`http://localhost:5000/api/upazilas/${form.zilaId}`).then(res => res.json()).then(setUpazilas);
    } else {
      setUpazilas([]);
    }
    setForm(f => ({ ...f, upazilaId: "" }));
  }, [form.zilaId]);

  const handleChange = (key: string, value: any) => setForm(f => ({ ...f, [key]: value }));

  const handleSubmit = async () => {
    if (!form.firstName || !form.lastName || !form.bloodGroup || !form.divisionId || !form.zilaId || !form.upazilaId || !form.currentLocation || !form.lastDonationDate || !form.phoneNumber || !form.consent) {
      Alert.alert("Error", "Please fill all required fields and give consent.");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch("http://localhost:5000/api/donors", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(form),
      });
      if (res.ok) {
        Alert.alert("Success", "Donor registered successfully");
        navigation.goBack();
      } else {
        const data = await res.json();
        Alert.alert("Error", data.error || "Registration failed");
      }
    } catch (e) {
      Alert.alert("Error", "Network error");
    }
    setLoading(false);
  };

  return (
    <ScrollView contentContainerStyle={{ padding: 16 }}>
      <Text>First Name*</Text>
      <TextInput value={form.firstName} onChangeText={v => handleChange("firstName", v)} style={{ borderWidth: 1, marginBottom: 8 }} />
      <Text>Last Name*</Text>
      <TextInput value={form.lastName} onChangeText={v => handleChange("lastName", v)} style={{ borderWidth: 1, marginBottom: 8 }} />
      <Text>Blood Group*</Text>
      <TextInput value={form.bloodGroup} onChangeText={v => handleChange("bloodGroup", v)} style={{ borderWidth: 1, marginBottom: 8 }} />
      <Text>Division*</Text>
      <Picker selectedValue={form.divisionId} onValueChange={v => handleChange("divisionId", v)} style={{ borderWidth: 1, marginBottom: 8 }}>
        <Picker.Item label="Select Division" value="" />
        {divisions.map((d: any) => <Picker.Item key={d.id} label={d.name} value={d.id.toString()} />)}
      </Picker>
      <Text>Zila*</Text>
      <Picker selectedValue={form.zilaId} onValueChange={v => handleChange("zilaId", v)} style={{ borderWidth: 1, marginBottom: 8 }} enabled={!!form.divisionId}>
        <Picker.Item label="Select Zila" value="" />
        {zilas.map((z: any) => <Picker.Item key={z.id} label={z.name} value={z.id.toString()} />)}
      </Picker>
      <Text>Upazila*</Text>
      <Picker selectedValue={form.upazilaId} onValueChange={v => handleChange("upazilaId", v)} style={{ borderWidth: 1, marginBottom: 8 }} enabled={!!form.zilaId}>
        <Picker.Item label="Select Upazila" value="" />
        {upazilas.map((u: any) => <Picker.Item key={u.id} label={u.name} value={u.id.toString()} />)}
      </Picker>
      <Text>Village</Text>
      <TextInput value={form.village} onChangeText={v => handleChange("village", v)} style={{ borderWidth: 1, marginBottom: 8 }} />
      <Text>Current Location*</Text>
      <TextInput value={form.currentLocation} onChangeText={v => handleChange("currentLocation", v)} style={{ borderWidth: 1, marginBottom: 8 }} />
      <Text>Last Donation Date*</Text>
      <TextInput value={form.lastDonationDate} onChangeText={v => handleChange("lastDonationDate", v)} style={{ borderWidth: 1, marginBottom: 8 }} placeholder="YYYY-MM-DD" />
      <Text>Phone Number*</Text>
      <TextInput value={form.phoneNumber} onChangeText={v => handleChange("phoneNumber", v)} style={{ borderWidth: 1, marginBottom: 8 }} keyboardType="phone-pad" />
      <Text>Available</Text>
      <Switch value={form.isAvailable} onValueChange={v => handleChange("isAvailable", v)} />
      <Text>Notes</Text>
      <TextInput value={form.notes} onChangeText={v => handleChange("notes", v)} style={{ borderWidth: 1, marginBottom: 8 }} />
      <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 8 }}>
        <Switch value={form.consent} onValueChange={v => handleChange("consent", v)} />
        <Text style={{ marginLeft: 8 }}>I consent to data usage*</Text>
      </View>
      {loading ? <ActivityIndicator /> : <Button title="Register" onPress={handleSubmit} />}
    </ScrollView>
  );
} 