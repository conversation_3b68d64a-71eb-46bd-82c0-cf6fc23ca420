{"version": 3, "names": ["nanoid", "React", "useLatestCallback", "useNavigation", "usePreventRemoveContext", "useRoute", "usePreventRemove", "preventRemove", "callback", "id", "useState", "navigation", "key", "routeKey", "setPreventRemove", "useEffect", "beforeRemoveListener", "e", "preventDefault", "data", "addListener"], "sourceRoot": "../../src", "sources": ["usePreventRemove.tsx"], "mappings": ";;AACA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAGnD,SAASC,aAAa,QAAQ,oBAAiB;AAC/C,SAASC,uBAAuB,QAAQ,8BAA2B;AACnE,SAASC,QAAQ,QAAQ,eAAY;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAC9BC,aAAsB,EACtBC,QAAmE,EACnE;EACA,MAAM,CAACC,EAAE,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC,MAAMV,MAAM,CAAC,CAAC,CAAC;EAE3C,MAAMW,UAAU,GAAGR,aAAa,CAAC,CAAC;EAClC,MAAM;IAAES,GAAG,EAAEC;EAAS,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAEpC,MAAM;IAAES;EAAiB,CAAC,GAAGV,uBAAuB,CAAC,CAAC;EAEtDH,KAAK,CAACc,SAAS,CAAC,MAAM;IACpBD,gBAAgB,CAACL,EAAE,EAAEI,QAAQ,EAAEN,aAAa,CAAC;IAC7C,OAAO,MAAM;MACXO,gBAAgB,CAACL,EAAE,EAAEI,QAAQ,EAAE,KAAK,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACC,gBAAgB,EAAEL,EAAE,EAAEI,QAAQ,EAAEN,aAAa,CAAC,CAAC;EAEnD,MAAMS,oBAAoB,GAAGd,iBAAiB,CAE3Ce,CAAC,IAAK;IACP,IAAI,CAACV,aAAa,EAAE;MAClB;IACF;IAEAU,CAAC,CAACC,cAAc,CAAC,CAAC;IAElBV,QAAQ,CAAC;MAAEW,IAAI,EAAEF,CAAC,CAACE;IAAK,CAAC,CAAC;EAC5B,CAAC,CAAC;EAEFlB,KAAK,CAACc,SAAS,CACb,MAAMJ,UAAU,EAAES,WAAW,CAAC,cAAc,EAAEJ,oBAAoB,CAAC,EACnE,CAACL,UAAU,EAAEK,oBAAoB,CACnC,CAAC;AACH", "ignoreList": []}