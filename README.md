# LifeDonor

LifeDonor is a full-stack application for managing blood donors, built with a Node.js/Express backend and a React Native frontend.

## Project Structure

- `backend/` - Node.js + Express REST API
- `frontend/` - React Native mobile app

## Getting Started

### Backend

1. `cd backend`
2. Install dependencies: `npm install`
3. Start server: `npm start`

### Frontend

1. `cd frontend`
2. Install dependencies: `npm install`
3. Start app: `npm start` (or use Expo if configured)

## Contributing

- Please do not commit `node_modules` or sensitive files.
- See `.gitignore` for ignored files.

## License

MIT 