{"version": 3, "names": ["getActionFromState", "state", "options", "normalizedConfig", "createNormalizedConfigItem", "routes", "index", "slice", "length", "undefined", "key", "name", "initialRouteName", "type", "payload", "route", "current", "config", "screens", "params", "path", "Object", "keys", "pop", "assign", "initial", "screen", "createNormalizedConfigs", "entries", "reduce", "acc", "k", "v"], "sourceRoot": "../../src", "sources": ["getActionFromState.tsx"], "mappings": ";;AA8BA,OAAO,SAASA,kBAAkBA,CAChCC,KAAoC,EACpCC,OAAiB,EACmD;EACpE;EACA,MAAMC,gBAAgB,GAAGD,OAAO,GAC5BE,0BAA0B,CAACF,OAAsC,CAAC,GAClE,CAAC,CAAC;EAEN,MAAMG,MAAM,GACVJ,KAAK,CAACK,KAAK,IAAI,IAAI,GAAGL,KAAK,CAACI,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEN,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC,GAAGL,KAAK,CAACI,MAAM;EAE7E,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOC,SAAS;EAClB;EAEA,IACE,EACGJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAClDJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAClBH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAC3BJ,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,KAAKR,gBAAgB,EAAES,gBAAgB,IACrDP,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAU,CAC/B,EACD;IACA,OAAO;MACLI,IAAI,EAAE,OAAO;MACbC,OAAO,EAAEb;IACX,CAAC;EACH;EAEA,MAAMc,KAAK,GAAGd,KAAK,CAACI,MAAM,CAACJ,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACI,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAElE,IAAIQ,OAAkD,GAAGD,KAAK,EAAEd,KAAK;EACrE,IAAIgB,MAA8B,GAAGd,gBAAgB,EAAEe,OAAO,GAAGH,KAAK,EAAEJ,IAAI,CAAC;EAC7E,IAAIQ,MAAM,GAAG;IAAE,GAAGJ,KAAK,CAACI;EAAO,CAAyC;EAExE,MAAML,OAOO,GAAGC,KAAK,GACjB;IAAEJ,IAAI,EAAEI,KAAK,CAACJ,IAAI;IAAES,IAAI,EAAEL,KAAK,CAACK,IAAI;IAAED;EAAO,CAAC,GAC9CV,SAAS;;EAEb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIK,OAAO,IAAIG,MAAM,EAAEC,OAAO,IAAIG,MAAM,CAACC,IAAI,CAACL,MAAM,CAACC,OAAO,CAAC,CAACV,MAAM,EAAE;IACpEM,OAAO,CAACS,GAAG,GAAG,IAAI;EACpB;EAEA,OAAOP,OAAO,EAAE;IACd,IAAIA,OAAO,CAACX,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAOC,SAAS;IAClB;IAEA,MAAMJ,MAAM,GACVW,OAAO,CAACV,KAAK,IAAI,IAAI,GACjBU,OAAO,CAACX,MAAM,CAACE,KAAK,CAAC,CAAC,EAAES,OAAO,CAACV,KAAK,GAAG,CAAC,CAAC,GAC1CU,OAAO,CAACX,MAAM;IAEpB,MAAMU,KAAkD,GACtDV,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;;IAE3B;IACAa,MAAM,CAACG,MAAM,CAACL,MAAM,EAAE;MACpBM,OAAO,EAAEhB,SAAS;MAClBiB,MAAM,EAAEjB,SAAS;MACjBU,MAAM,EAAEV,SAAS;MACjBR,KAAK,EAAEQ;IACT,CAAC,CAAC;IAEF,IAAIJ,MAAM,CAACG,MAAM,KAAK,CAAC,IAAIH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,EAAE;MACtDU,MAAM,CAACM,OAAO,GAAG,IAAI;MACrBN,MAAM,CAACO,MAAM,GAAGX,KAAK,CAACJ,IAAI;IAC5B,CAAC,MAAM,IACLN,MAAM,CAACG,MAAM,KAAK,CAAC,IACnBH,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,IAC3BJ,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,KAAKM,MAAM,EAAEL,gBAAgB,IAC3CP,MAAM,CAAC,CAAC,CAAC,CAACK,GAAG,KAAKD,SAAS,EAC3B;MACAU,MAAM,CAACM,OAAO,GAAG,KAAK;MACtBN,MAAM,CAACO,MAAM,GAAGX,KAAK,CAACJ,IAAI;IAC5B,CAAC,MAAM;MACLQ,MAAM,CAAClB,KAAK,GAAGe,OAAO;MACtB;IACF;IAEA,IAAID,KAAK,CAACd,KAAK,EAAE;MACfkB,MAAM,CAACA,MAAM,GAAG;QAAE,GAAGJ,KAAK,CAACI;MAAO,CAAC;MACnCA,MAAM,CAACI,GAAG,GAAG,IAAI;MACjBJ,MAAM,GAAGA,MAAM,CAACA,MAA8C;IAChE,CAAC,MAAM;MACLA,MAAM,CAACC,IAAI,GAAGL,KAAK,CAACK,IAAI;MACxBD,MAAM,CAACA,MAAM,GAAGJ,KAAK,CAACI,MAAM;IAC9B;IAEAH,OAAO,GAAGD,KAAK,CAACd,KAAK;IACrBgB,MAAM,GAAGA,MAAM,EAAEC,OAAO,GAAGH,KAAK,CAACJ,IAAI,CAAC;IAEtC,IAAIM,MAAM,EAAEC,OAAO,IAAIG,MAAM,CAACC,IAAI,CAACL,MAAM,CAACC,OAAO,CAAC,CAACV,MAAM,EAAE;MACzDW,MAAM,CAACI,GAAG,GAAG,IAAI;IACnB;EACF;EAEA,IAAIT,OAAO,EAAEK,MAAM,CAACO,MAAM,IAAIZ,OAAO,EAAEK,MAAM,CAAClB,KAAK,EAAE;IACnDa,OAAO,CAACS,GAAG,GAAG,IAAI;EACpB;EAEA,IAAI,CAACT,OAAO,EAAE;IACZ;EACF;;EAEA;EACA;EACA,OAAO;IACLD,IAAI,EAAE,UAAU;IAChBC;EACF,CAAC;AACH;AAEA,MAAMV,0BAA0B,GAAIa,MAAmC,IACrE,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,IAAI,GACxC;EACEL,gBAAgB,EAAEK,MAAM,CAACL,gBAAgB;EACzCM,OAAO,EACLD,MAAM,CAACC,OAAO,IAAI,IAAI,GAClBS,uBAAuB,CAACV,MAAM,CAACC,OAAO,CAAC,GACvCT;AACR,CAAC,GACD,CAAC,CAAC;AAER,MAAMkB,uBAAuB,GAAIzB,OAA8B,IAC7DmB,MAAM,CAACO,OAAO,CAAC1B,OAAO,CAAC,CAAC2B,MAAM,CAA6B,CAACC,GAAG,EAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK;EAC1EF,GAAG,CAACC,CAAC,CAAC,GAAG3B,0BAA0B,CAAC4B,CAAC,CAAC;EACtC,OAAOF,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC", "ignoreList": []}