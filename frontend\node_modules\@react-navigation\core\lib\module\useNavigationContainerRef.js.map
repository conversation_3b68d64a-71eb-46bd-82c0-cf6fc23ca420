{"version": 3, "names": ["React", "createNavigationContainerRef", "useNavigationContainerRef", "navigation", "useRef", "current"], "sourceRoot": "../../src", "sources": ["useNavigationContainerRef.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,4BAA4B,QAAQ,mCAAgC;AAG7E,OAAO,SAASC,yBAAyBA,CAAA,EAES;EAChD,MAAMC,UAAU,GACdH,KAAK,CAACI,MAAM,CAAsD,IAAI,CAAC;EAEzE,IAAID,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;IAC9BF,UAAU,CAACE,OAAO,GAAGJ,4BAA4B,CAAY,CAAC;EAChE;EAEA,OAAOE,UAAU,CAACE,OAAO;AAC3B", "ignoreList": []}