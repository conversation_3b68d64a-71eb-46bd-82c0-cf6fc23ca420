/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNSBottomTabsScreenManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNSBottomTabsScreenManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNSBottomTabsScreenManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "isFocused":
        mViewManager.setIsFocused(view, value == null ? false : (boolean) value);
        break;
      case "tabKey":
        mViewManager.setTabKey(view, value == null ? null : (String) value);
        break;
      case "tabBarBackgroundColor":
        mViewManager.setTabBarBackgroundColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarBlurEffect":
        mViewManager.setTabBarBlurEffect(view, (String) value);
        break;
      case "tabBarItemTitleFontFamily":
        mViewManager.setTabBarItemTitleFontFamily(view, value == null ? null : (String) value);
        break;
      case "tabBarItemTitleFontSize":
        mViewManager.setTabBarItemTitleFontSize(view, value == null ? 0f : ((Double) value).floatValue());
        break;
      case "tabBarItemTitleFontWeight":
        mViewManager.setTabBarItemTitleFontWeight(view, value == null ? null : (String) value);
        break;
      case "tabBarItemTitleFontStyle":
        mViewManager.setTabBarItemTitleFontStyle(view, value == null ? null : (String) value);
        break;
      case "tabBarItemTitleFontColor":
        mViewManager.setTabBarItemTitleFontColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemTitlePositionAdjustment":
        mViewManager.setTabBarItemTitlePositionAdjustment(view, (ReadableMap) value);
        break;
      case "tabBarItemIconColor":
        mViewManager.setTabBarItemIconColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemBadgeBackgroundColor":
        mViewManager.setTabBarItemBadgeBackgroundColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "title":
        mViewManager.setTitle(view, value == null ? null : (String) value);
        break;
      case "iconResourceName":
        mViewManager.setIconResourceName(view, value == null ? null : (String) value);
        break;
      case "iconType":
        mViewManager.setIconType(view, (String) value);
        break;
      case "iconImageSource":
        mViewManager.setIconImageSource(view, (ReadableMap) value);
        break;
      case "iconSfSymbolName":
        mViewManager.setIconSfSymbolName(view, value == null ? null : (String) value);
        break;
      case "selectedIconImageSource":
        mViewManager.setSelectedIconImageSource(view, (ReadableMap) value);
        break;
      case "selectedIconSfSymbolName":
        mViewManager.setSelectedIconSfSymbolName(view, value == null ? null : (String) value);
        break;
      case "badgeValue":
        mViewManager.setBadgeValue(view, value == null ? null : (String) value);
        break;
      case "specialEffects":
        mViewManager.setSpecialEffects(view, (ReadableMap) value);
        break;
      case "overrideScrollViewContentInsetAdjustmentBehavior":
        mViewManager.setOverrideScrollViewContentInsetAdjustmentBehavior(view, value == null ? true : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
