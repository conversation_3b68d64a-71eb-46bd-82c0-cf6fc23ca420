{"version": 3, "names": ["CHILD_STATE", "getFocusedRouteNameFromRoute", "route", "state", "params", "routeName", "routes", "index", "type", "length", "name", "screen", "undefined"], "sourceRoot": "../../src", "sources": ["getFocusedRouteNameFromRoute.tsx"], "mappings": ";;AAEA,SAASA,WAAW,QAAQ,oBAAiB;AAE7C,OAAO,SAASC,4BAA4BA,CAC1CC,KAA6B,EACT;EACpB;EACA,MAAMC,KAAK,GAAGD,KAAK,CAACF,WAAW,CAAC,IAAIE,KAAK,CAACC,KAAK;EAC/C,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAA0C;EAE/D,MAAMC,SAAS,GAAGF,KAAK;EACnB;EACAA,KAAK,CAACG,MAAM;EACV;EACA;EACAH,KAAK,CAACI,KAAK,KACR,OAAOJ,KAAK,CAACK,IAAI,KAAK,QAAQ,IAAIL,KAAK,CAACK,IAAI,KAAK,OAAO,GACrD,CAAC,GACDL,KAAK,CAACG,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAC/B,CAACC,IAAI;EACN;EACA,OAAON,MAAM,EAAEO,MAAM,KAAK,QAAQ,GAChCP,MAAM,CAACO,MAAM,GACbC,SAAS;EAEf,OAAOP,SAAS;AAClB", "ignoreList": []}