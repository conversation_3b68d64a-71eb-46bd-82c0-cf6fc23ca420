{"version": 3, "names": ["React", "isRecordEqual", "CHILD_STATE", "Symbol", "useRouteCache", "routes", "cache", "useMemo", "current", "Map", "process", "env", "NODE_ENV", "reduce", "acc", "route", "previous", "get", "key", "state", "routeWithoutState", "proxy", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "set", "Array", "from", "values"], "sourceRoot": "../../src", "sources": ["useRouteCache.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAa,QAAQ,oBAAiB;AAK/C;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAGC,MAAM,CAAC,aAAa,CAAC;;AAEhD;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAC3BC,MAAuB,EACvB;EACA;EACA,MAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,CAAC,OAAO;IAAEC,OAAO,EAAE,IAAIC,GAAG,CAAC;EAAgB,CAAC,CAAC,EAAE,EAAE,CAAC;EAE7E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,OAAOP,MAAM;EACf;EAEAC,KAAK,CAACE,OAAO,GAAGH,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC5C,MAAMC,QAAQ,GAAGV,KAAK,CAACE,OAAO,CAACS,GAAG,CAACF,KAAK,CAACG,GAAG,CAAC;IAC7C,MAAM;MAAEC,KAAK;MAAE,GAAGC;IAAkB,CAAC,GAAGL,KAAK;IAE7C,IAAIM,KAAK;IAET,IAAIL,QAAQ,IAAIf,aAAa,CAACe,QAAQ,EAAEI,iBAAiB,CAAC,EAAE;MAC1D;MACAC,KAAK,GAAGL,QAAQ;IAClB,CAAC,MAAM;MACLK,KAAK,GAAGD,iBAAiB;IAC3B;IAEA,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA;MACA,KAAK,MAAMM,GAAG,IAAIG,KAAK,EAAE;QACvB;QACA,MAAMC,KAAK,GAAGD,KAAK,CAACH,GAAG,CAAC;QAExBK,MAAM,CAACC,cAAc,CAACH,KAAK,EAAEH,GAAG,EAAE;UAChCO,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE,KAAK;UACfL;QACF,CAAC,CAAC;MACJ;IACF;IAEAC,MAAM,CAACC,cAAc,CAACH,KAAK,EAAEnB,WAAW,EAAE;MACxCuB,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,IAAI;MAClBJ,KAAK,EAAEH;IACT,CAAC,CAAC;IAEFL,GAAG,CAACc,GAAG,CAACb,KAAK,CAACG,GAAG,EAAEG,KAAK,CAAC;IAEzB,OAAOP,GAAG;EACZ,CAAC,EAAE,IAAIL,GAAG,CAAC,CAAe,CAAC;EAE3B,OAAOoB,KAAK,CAACC,IAAI,CAACxB,KAAK,CAACE,OAAO,CAACuB,MAAM,CAAC,CAAC,CAAC;AAC3C", "ignoreList": []}