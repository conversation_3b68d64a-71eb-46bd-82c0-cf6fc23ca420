{"name": "is-plain-obj", "version": "2.1.0", "description": "Check if a value is a plain object", "license": "MIT", "repository": "sindresorhus/is-plain-obj", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}