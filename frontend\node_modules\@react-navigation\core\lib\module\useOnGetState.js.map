{"version": 3, "names": ["React", "isArrayEqual", "NavigationBuilderContext", "NavigationRouteContext", "useOnGetState", "getState", "getStateListeners", "addKeyedListener", "useContext", "route", "key", "getRehydratedState", "useCallback", "state", "routes", "map", "childState", "useEffect"], "sourceRoot": "../../src", "sources": ["useOnGetState.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAY,QAAQ,mBAAgB;AAC7C,SAEEC,wBAAwB,QACnB,+BAA4B;AACnC,SAASC,sBAAsB,QAAQ,6BAA0B;AAOjE,OAAO,SAASC,aAAaA,CAAC;EAAEC,QAAQ;EAAEC;AAA2B,CAAC,EAAE;EACtE,MAAM;IAAEC;EAAiB,CAAC,GAAGP,KAAK,CAACQ,UAAU,CAACN,wBAAwB,CAAC;EACvE,MAAMO,KAAK,GAAGT,KAAK,CAACQ,UAAU,CAACL,sBAAsB,CAAC;EACtD,MAAMO,GAAG,GAAGD,KAAK,GAAGA,KAAK,CAACC,GAAG,GAAG,MAAM;EAEtC,MAAMC,kBAAkB,GAAGX,KAAK,CAACY,WAAW,CAAC,MAAM;IACjD,MAAMC,KAAK,GAAGR,QAAQ,CAAC,CAAC;;IAExB;IACA,MAAMS,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACC,GAAG,CAAEN,KAAK,IAAK;MACzC,MAAMO,UAAU,GAAGV,iBAAiB,CAACG,KAAK,CAACC,GAAG,CAAC,GAAG,CAAC;MAEnD,IAAID,KAAK,CAACI,KAAK,KAAKG,UAAU,EAAE;QAC9B,OAAOP,KAAK;MACd;MAEA,OAAO;QAAE,GAAGA,KAAK;QAAEI,KAAK,EAAEG;MAAW,CAAC;IACxC,CAAC,CAAC;IAEF,IAAIf,YAAY,CAACY,KAAK,CAACC,MAAM,EAAEA,MAAM,CAAC,EAAE;MACtC,OAAOD,KAAK;IACd;IAEA,OAAO;MAAE,GAAGA,KAAK;MAAEC;IAAO,CAAC;EAC7B,CAAC,EAAE,CAACT,QAAQ,EAAEC,iBAAiB,CAAC,CAAC;EAEjCN,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,OAAOV,gBAAgB,GAAG,UAAU,EAAEG,GAAG,EAAEC,kBAAkB,CAAC;EAChE,CAAC,EAAE,CAACJ,gBAAgB,EAAEI,kBAAkB,EAAED,GAAG,CAAC,CAAC;AACjD", "ignoreList": []}