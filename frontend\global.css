@tailwind base;
@tailwind components;
@tailwind utilities;

/* LifeDonor Global Styles & Theme */
:root {
  --color-primary: #D32F2F;
  --color-primary-dark: #C62828;
  --color-secondary: #FFFFFF;
  --color-text: #333333;
  --color-accent: #1976D2;
  --color-safe: #4CAF50;
  --color-alert: #FFC107;
  --font-family-base: 'Roboto', 'San Francisco', Arial, sans-serif;
  --font-size-base: 16px;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --border-radius: 12px;
  --transition: 0.2s cubic-bezier(0.4,0,0.2,1);
}

/* High Contrast Mode */
.high-contrast {
  --color-primary: #000000;
  --color-secondary: #FFFFFF;
  --color-text: #000000;
  --color-accent: #FFD600;
  --color-safe: #00E676;
  --color-alert: #FF3D00;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  color: var(--color-text);
  background: var(--color-secondary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--color-primary-dark);
  margin-top: 0;
}

button, .btn {
  font-family: inherit;
  font-size: 1rem;
  border-radius: var(--border-radius);
  transition: background var(--transition), color var(--transition);
  cursor: pointer;
}

input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid #ccc;
  padding: 0.5em 1em;
  margin-bottom: 1em;
}

/* Accessibility: Large Text */
body.large-text {
  font-size: 1.25rem;
}

/* Utility classes */
.text-primary { color: var(--color-primary); }
.text-accent { color: var(--color-accent); }
.text-safe { color: var(--color-safe); }
.text-alert { color: var(--color-alert); }
.bg-primary { background: var(--color-primary); color: #fff; }
.bg-accent { background: var(--color-accent); color: #fff; }
.bg-safe { background: var(--color-safe); color: #fff; }
.bg-alert { background: var(--color-alert); color: #333; }
