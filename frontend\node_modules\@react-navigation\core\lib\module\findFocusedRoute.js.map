{"version": 3, "names": ["findFocusedRoute", "state", "current", "routes", "index", "route"], "sourceRoot": "../../src", "sources": ["findFocusedRoute.tsx"], "mappings": ";;AAWA,OAAO,SAASA,gBAAgBA,CAACC,KAAmB,EAAU;EAC5D,IAAIC,OAAiC,GAAGD,KAAK;EAE7C,OAAOC,OAAO,EAAEC,MAAM,CAACD,OAAO,CAACE,KAAK,IAAI,CAAC,CAAC,CAACH,KAAK,IAAI,IAAI,EAAE;IACxDC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACD,OAAO,CAACE,KAAK,IAAI,CAAC,CAAC,CAACH,KAAK;EACpD;EAEA,MAAMI,KAAK,GAAGH,OAAO,EAAEC,MAAM,CAACD,OAAO,EAAEE,KAAK,IAAI,CAAC,CAAC;EAElD,OAAOC,KAAK;AACd", "ignoreList": []}